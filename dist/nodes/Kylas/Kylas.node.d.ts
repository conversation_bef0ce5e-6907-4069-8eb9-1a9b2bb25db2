import { INodeType, INodeTypeDescription, ILoadOptionsFunctions, INodePropertyOptions } from 'n8n-workflow';
export declare class <PERSON>yla<PERSON> implements INodeType {
    description: INodeTypeDescription;
    methods: {
        loadOptions: {
            getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
            getPicklistValues(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
            getLeadCustomFieldsWithType(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
        };
    };
}
