"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customFieldsDescription = void 0;
const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};
const fieldTypeMapping = {
    'TEXT_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'Text input field'
    },
    'NUMBER_FIELD': {
        n8nType: 'number',
        defaultValue: 0,
        description: 'Number input field'
    },
    'DATE_FIELD': {
        n8nType: 'dateTime',
        defaultValue: '',
        description: 'Date and time picker'
    },
    'BOOLEAN_FIELD': {
        n8nType: 'boolean',
        defaultValue: false,
        description: 'Checkbox field'
    },
    'PICKLIST': {
        n8nType: 'options',
        defaultValue: '',
        description: 'Dropdown selection field'
    },
    'EMAIL_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'Email input field'
    },
    'URL_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'URL input field'
    },
    'TEXTAREA_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'Multi-line text input'
    }
};
exports.customFieldsDescription = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Custom Field",
                name: "customField",
                values: [
                    {
                        displayName: "Field",
                        name: "fieldName",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: "getLeadCustomFieldsWithType",
                        },
                        default: "",
                        description: "Select a custom field",
                    },
                    {
                        displayName: "Value (Text)",
                        name: "textValue",
                        type: "string",
                        default: "",
                        description: "Enter text value",
                        displayOptions: {
                            show: {
                                fieldName: ['!=', ''],
                                '@version': [1],
                            },
                            hide: {
                                fieldName: ['/PICKLIST|NUMBER_FIELD|DATE_FIELD|BOOLEAN_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Number)",
                        name: "numberValue",
                        type: "number",
                        default: 0,
                        description: "Enter number value",
                        displayOptions: {
                            show: {
                                fieldName: ['/NUMBER_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Date)",
                        name: "dateValue",
                        type: "dateTime",
                        default: "",
                        description: "Enter date value",
                        displayOptions: {
                            show: {
                                fieldName: ['/DATE_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Boolean)",
                        name: "booleanValue",
                        type: "boolean",
                        default: false,
                        description: "Select true or false",
                        displayOptions: {
                            show: {
                                fieldName: ['/BOOLEAN_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Picklist)",
                        name: "picklistValue",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: "getPicklistValues",
                            loadOptionsDependsOn: ["fieldName"],
                        },
                        default: "",
                        description: "Select from available options",
                        displayOptions: {
                            show: {
                                fieldName: ['/PICKLIST/'],
                            }
                        }
                    },
                ],
            },
        ],
        routing: {
            send: {
                preSend: [
                    async function (requestOptions) {
                        const customFields = this.getNodeParameter('customFields.customField', []);
                        customFields.forEach(field => {
                            if (field.fieldName) {
                                try {
                                    const fieldMeta = JSON.parse(field.fieldName);
                                    const fieldName = fieldMeta.name;
                                    const fieldType = fieldMeta.type;
                                    let value;
                                    switch (fieldType) {
                                        case 'TEXT_FIELD':
                                        case 'EMAIL_FIELD':
                                        case 'URL_FIELD':
                                        case 'TEXTAREA_FIELD':
                                            value = field.textValue;
                                            break;
                                        case 'NUMBER_FIELD':
                                            value = field.numberValue;
                                            break;
                                        case 'DATE_FIELD':
                                            value = field.dateValue;
                                            break;
                                        case 'BOOLEAN_FIELD':
                                            value = field.booleanValue;
                                            break;
                                        case 'PICKLIST':
                                            value = field.picklistValue;
                                            break;
                                        default:
                                            value = field.textValue;
                                    }
                                    if (value !== undefined && value !== '' && value !== null) {
                                        requestOptions.body[fieldName] = value;
                                    }
                                }
                                catch (error) {
                                    const value = field.textValue || field.numberValue || field.dateValue || field.booleanValue || field.picklistValue;
                                    if (value !== undefined && value !== '' && value !== null) {
                                        requestOptions.body[field.fieldName] = value;
                                    }
                                }
                            }
                        });
                        return requestOptions;
                    }
                ]
            },
        },
    },
];
//# sourceMappingURL=customFields.js.map