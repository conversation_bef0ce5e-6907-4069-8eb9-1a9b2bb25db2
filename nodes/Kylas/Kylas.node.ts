import { userDescription } from './resources/user';
import { companyDescription } from './resources/company';
import { leadDescription } from './resources/lead';
import {
	INodeType,
	INodeTypeDescription,
	ILoadOptionsFunctions,
	INodePropertyOptions,
	IHttpRequestOptions
} from 'n8n-workflow';

export class <PERSON><PERSON><PERSON> implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Kyla<PERSON>',
		name: 'kyla<PERSON>',
		icon: { light: 'file:kylas.svg', dark: 'file:kylas.dark.svg' },
		group: ['transform'],
		version: 1,
		subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
		description: 'Interact with the Kylas API',
		defaults: {
			name: '<PERSON>yla<PERSON>',
		},
		usableAsTool: true,
		inputs: ['main'],
		outputs: ['main'],
		credentials: [{ name: 'kylasApi', required: true }],
		requestDefaults: {
			baseURL: 'https://api-qa.sling-dev.com',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json',
			},
		},
		properties: [
			{
				displayName: 'Resource',
				name: 'resource',
				type: 'options',
				noDataExpression: true,
				options: [
					{
						name: 'User',
						value: 'user',
					},
					{
						name: 'Company',
						value: 'company',
					},
					{
						name: 'Lead',
						value: 'lead',
					},
				],
				default: 'user',
			},
			...userDescription,
			...companyDescription,
			...leadDescription
		],
	};

	methods = {
		loadOptions: {
			async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
				const requestOptions: IHttpRequestOptions = {
					method: 'GET',
					url: '/v1/layouts/leads/system-fields',
					qs: {
						view: 'create'
					},
					json: true,
				};

				const response = await this.helpers.httpRequestWithAuthentication.call(this, 'kylasApi', requestOptions);
				const fields = response as Array<{
					id: number;
					type: string;
					displayName: string;
					name: string;
					active: boolean;
					required: boolean;
					important: boolean;
					standard: boolean;
					width: number;
					column: number;
					row: number;
					multiValue: boolean;
					internal: boolean;
					picklist: {
						id?: number;
						name?: string;
					} | null;
					systemRequired: boolean;
				}>;

				// Get field type filter from parameters if provided
				const currentNodeParameters = this.getCurrentNodeParameters();
				const fieldType = currentNodeParameters?.fieldType as string;

				// Filter for custom fields (standard=false and internal=false)
				let customFields = fields.filter(field =>
					field.active &&
					!field.standard &&
					!field.internal
				);

				// Further filter by field type if specified
				if (fieldType) {
					customFields = customFields.filter(field => field.type === fieldType);
				}

				return customFields.map(field => ({
					name: field.displayName,
					value: field.name,
					description: `Type: ${field.type}${field.required ? ' (Required)' : ''}`,
				}));
			},

			async getPicklistValues(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
				const currentNodeParameters = this.getCurrentNodeParameters();
				const fieldName = currentNodeParameters?.fieldName as string;

				if (!fieldName) {
					return [];
				}

				try {
					// Parse the field metadata
					const fieldMeta = JSON.parse(fieldName);

					if (!fieldMeta.picklist?.id) {
						return [];
					}

					// Fetch the picklist values
					const picklistRequestOptions: IHttpRequestOptions = {
						method: 'GET',
						url: `/v1/picklists/${fieldMeta.picklist.id}/values`,
						json: true,
					};

					const picklistResponse = await this.helpers.httpRequestWithAuthentication.call(this, 'kylasApi', picklistRequestOptions);
					const picklistValues = picklistResponse as Array<{
						id: number;
						value: string;
						displayName: string;
						active: boolean;
					}>;

					return picklistValues
						.filter(value => value.active)
						.map(value => ({
							name: value.displayName,
							value: value.value,
						}));
				} catch {
					// If parsing fails, return empty array
					return [];
				}
			},

			async getLeadCustomFieldsWithType(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
				const requestOptions: IHttpRequestOptions = {
					method: 'GET',
					url: '/v1/layouts/leads/system-fields',
					qs: {
						view: 'create'
					},
					json: true,
				};

				const response = await this.helpers.httpRequestWithAuthentication.call(this, 'kylasApi', requestOptions);
				console.log("response =>"+JSON.stringify(response));
				const fields = response as Array<{
					id: number;
					type: string;
					displayName: string;
					name: string;
					active: boolean;
					required: boolean;
					important: boolean;
					standard: boolean;
					width: number;
					column: number;
					row: number;
					multiValue: boolean;
					internal: boolean;
					picklist: {
						id?: number;
						name?: string;
					} | null;
					systemRequired: boolean;
				}>;

				// Filter for custom fields (standard=false and internal=false)
				const customFields = fields.filter(field =>
					field.active &&
					!field.standard &&
					!field.internal
				);

				return customFields.map(field => ({
					name: `${field.displayName} (${field.type})`,
					value: JSON.stringify({
						name: field.name,
						type: field.type,
						required: field.required,
						picklist: field.picklist
					}),
					description: `Type: ${field.type}${field.required ? ' (Required)' : ''}`,
				}));
			},
		},
	};

}
