import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};

// Field type mapping from API types to n8n field types
const fieldTypeMapping: Record<string, {
    n8nType: string;
    defaultValue: any;
    description: string;
}> = {
    'TEXT_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'Text input field'
    },
    'NUMBER_FIELD': {
        n8nType: 'number',
        defaultValue: 0,
        description: 'Number input field'
    },
    'DATE_FIELD': {
        n8nType: 'dateTime',
        defaultValue: '',
        description: 'Date and time picker'
    },
    'BOOLEAN_FIELD': {
        n8nType: 'boolean',
        defaultValue: false,
        description: 'Checkbox field'
    },
    'PICKLIST': {
        n8nType: 'options',
        defaultValue: '',
        description: 'Dropdown selection field'
    },
    'EMAIL_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'Email input field'
    },
    'URL_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'URL input field'
    },
    'TEXTAREA_FIELD': {
        n8nType: 'string',
        defaultValue: '',
        description: 'Multi-line text input'
    }
};

export const customFieldsDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Custom Field",
                name: "customField",
                values: [
                    {
                        displayName: "Field",
                        name: "fieldName",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: "getLeadCustomFieldsWithType",
                        },
                        default: "",
                        description: "Select a custom field",
                    },
                    {
                        displayName: "Value (Text)",
                        name: "textValue",
                        type: "string",
                        default: "",
                        description: "Enter text value",
                        displayOptions: {
                            show: {
                                fieldName: ['!=', ''],
                                '@version': [1],
                            },
                            hide: {
                                fieldName: ['/PICKLIST|NUMBER_FIELD|DATE_FIELD|BOOLEAN_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Number)",
                        name: "numberValue",
                        type: "number",
                        default: 0,
                        description: "Enter number value",
                        displayOptions: {
                            show: {
                                fieldName: ['/NUMBER_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Date)",
                        name: "dateValue",
                        type: "dateTime",
                        default: "",
                        description: "Enter date value",
                        displayOptions: {
                            show: {
                                fieldName: ['/DATE_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Boolean)",
                        name: "booleanValue",
                        type: "boolean",
                        default: false,
                        description: "Select true or false",
                        displayOptions: {
                            show: {
                                fieldName: ['/BOOLEAN_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Picklist)",
                        name: "picklistValue",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: "getPicklistValues",
                            loadOptionsDependsOn: ["fieldName"],
                        },
                        default: "",
                        description: "Select from available options",
                        displayOptions: {
                            show: {
                                fieldName: ['/PICKLIST/'],
                            }
                        }
                    },
                ],
            },
        ],
        routing: {
            send: {
                preSend: [
                    async function(this: any, requestOptions: any) {
                        const customFields = this.getNodeParameter('customFields.customField', []) as Array<{
                            fieldName: string;
                            textValue?: string;
                            numberValue?: number;
                            dateValue?: string;
                            booleanValue?: boolean;
                            picklistValue?: string;
                        }>;

                        // Add each custom field to the request body
                        customFields.forEach(field => {
                            if (field.fieldName) {
                                try {
                                    // Parse the field metadata from the fieldName value
                                    const fieldMeta = JSON.parse(field.fieldName);
                                    const fieldName = fieldMeta.name;
                                    const fieldType = fieldMeta.type;

                                    let value: any;

                                    // Determine the value based on field type
                                    switch (fieldType) {
                                        case 'TEXT_FIELD':
                                        case 'EMAIL_FIELD':
                                        case 'URL_FIELD':
                                        case 'TEXTAREA_FIELD':
                                            value = field.textValue;
                                            break;
                                        case 'NUMBER_FIELD':
                                            value = field.numberValue;
                                            break;
                                        case 'DATE_FIELD':
                                            value = field.dateValue;
                                            break;
                                        case 'BOOLEAN_FIELD':
                                            value = field.booleanValue;
                                            break;
                                        case 'PICKLIST':
                                            value = field.picklistValue;
                                            break;
                                        default:
                                            value = field.textValue; // fallback to text
                                    }

                                    // Only add if value is not empty/undefined
                                    if (value !== undefined && value !== '' && value !== null) {
                                        requestOptions.body[fieldName] = value;
                                    }
                                } catch (error) {
                                    // If parsing fails, treat as simple field name
                                    const value = field.textValue || field.numberValue || field.dateValue || field.booleanValue || field.picklistValue;
                                    if (value !== undefined && value !== '' && value !== null) {
                                        requestOptions.body[field.fieldName] = value;
                                    }
                                }
                            }
                        });

                        return requestOptions;
                    }
                ]
            },
        },
    },
];
