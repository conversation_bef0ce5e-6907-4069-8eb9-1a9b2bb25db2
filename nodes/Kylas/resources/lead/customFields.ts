import type { INodeProperties, IHttpRequestOptions } from 'n8n-workflow';

const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};



export const customFieldsDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Custom Field",
                name: "customField",
                values: [
                    {
                        displayName: "Field Name or ID",
                        name: "fieldName",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: "getLeadCustomFieldsWithType",
                        },
                        default: "",
                        description: "Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>",
                    },
                    {
                        displayName: "Value (Boolean)",
                        name: "booleanValue",
                        type: "boolean",
                        default: false,
                        description: "Whether to select true or false",
                        displayOptions: {
                            show: {
                                fieldName: ['/BOOLEAN_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Date)",
                        name: "dateValue",
                        type: "dateTime",
                        default: "",
                        description: "Enter date value",
                        displayOptions: {
                            show: {
                                fieldName: ['/DATE_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Number)",
                        name: "numberValue",
                        type: "number",
                        default: 0,
                        description: "Enter number value",
                        displayOptions: {
                            show: {
                                fieldName: ['/NUMBER_FIELD/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Picklist) Name or ID",
                        name: "picklistValue",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: "getPicklistValues",
                            loadOptionsDependsOn: ["fieldName"],
                        },
                        default: "",
                        description: "Choose from the list, or specify an ID using an <a href=\"https://docs.n8n.io/code/expressions/\">expression</a>",
                        displayOptions: {
                            show: {
                                fieldName: ['/PICKLIST/'],
                            }
                        }
                    },
                    {
                        displayName: "Value (Text)",
                        name: "textValue",
                        type: "string",
                        default: "",
                        description: "Enter text value",
                        displayOptions: {
                            show: {
                                fieldName: ['!=', ''],
                                '@version': [1],
                            },
                            hide: {
                                fieldName: ['/PICKLIST|NUMBER_FIELD|DATE_FIELD|BOOLEAN_FIELD/'],
                            }
                        }
                    },
                ],
            },
        ],
        routing: {
            send: {
                preSend: [
                    async function(this: {
                        getNodeParameter: (parameterName: string, defaultValue?: unknown) => unknown;
                    }, requestOptions: IHttpRequestOptions) {
                        const customFields = this.getNodeParameter('customFields.customField', []) as Array<{
                            fieldName: string;
                            textValue?: string;
                            numberValue?: number;
                            dateValue?: string;
                            booleanValue?: boolean;
                            picklistValue?: string;
                        }>;

                        // Add each custom field to the request body
                        customFields.forEach(field => {
                            if (field.fieldName) {
                                try {
                                    // Parse the field metadata from the fieldName value
                                    const fieldMeta = JSON.parse(field.fieldName);
                                    const fieldName = fieldMeta.name;
                                    const fieldType = fieldMeta.type;

                                    let value: unknown;

                                    // Determine the value based on field type
                                    switch (fieldType) {
                                        case 'TEXT_FIELD':
                                        case 'EMAIL_FIELD':
                                        case 'URL_FIELD':
                                        case 'TEXTAREA_FIELD':
                                            value = field.textValue;
                                            break;
                                        case 'NUMBER_FIELD':
                                            value = field.numberValue;
                                            break;
                                        case 'DATE_FIELD':
                                            value = field.dateValue;
                                            break;
                                        case 'BOOLEAN_FIELD':
                                            value = field.booleanValue;
                                            break;
                                        case 'PICKLIST':
                                            value = field.picklistValue;
                                            break;
                                        default:
                                            value = field.textValue; // fallback to text
                                    }

                                    // Only add if value is not empty/undefined
                                    if (value !== undefined && value !== '' && value !== null) {
                                        if (!requestOptions.body) {
                                            requestOptions.body = {};
                                        }
                                        if (typeof requestOptions.body === 'object' && requestOptions.body !== null && !Array.isArray(requestOptions.body)) {
                                            (requestOptions.body as Record<string, unknown>)[fieldName] = value;
                                        }
                                    }
                                } catch {
                                    // If parsing fails, treat as simple field name
                                    const value = field.textValue || field.numberValue || field.dateValue || field.booleanValue || field.picklistValue;
                                    if (value !== undefined && value !== '' && value !== null) {
                                        if (!requestOptions.body) {
                                            requestOptions.body = {};
                                        }
                                        if (typeof requestOptions.body === 'object' && requestOptions.body !== null && !Array.isArray(requestOptions.body)) {
                                            (requestOptions.body as Record<string, unknown>)[field.fieldName] = value;
                                        }
                                    }
                                }
                            }
                        });

                        return requestOptions;
                    }
                ]
            },
        },
    },
];
